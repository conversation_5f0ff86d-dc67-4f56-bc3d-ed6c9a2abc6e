// Simple test to check if NATS publishing works
import publishNatsMessage from './dist/app/helpers/publishNatsMessage.js';

console.log('Testing NATS publish...');

publishNatsMessage(
  'test.message', 
  'Hello from test!', 
  {}, 
  (error, ack) => {
    if (error) {
      console.error('❌ Publish failed:');
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    } else {
      console.log('✅ Publish successful!');
      console.log('Acknowledgment:', ack);
    }
    process.exit(0);
  }
);

// Timeout after 10 seconds
setTimeout(() => {
  console.error('❌ Test timed out after 10 seconds');
  process.exit(1);
}, 10000);
