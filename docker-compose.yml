services:
  fsdata:
    container_name: fsdata
    image: fsdata
    build:
      context: .
    env_file:
      - ./env/production.env
    expose:
      - 8092
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/usr/src/app/logs
      - ./file-upload:/usr/src/app/file-upload
    networks:
      - app-network
    restart: unless-stopped

  # see: https://hub.docker.com/_/mongo/
  # https://pimylifeup.com/mongodb-docker-compose/
  # https://medium.com/@arievrchman/setting-up-mongodb-single-node-with-replica-set-and-authentication-for-dockerized-environment-b77b494a63d3
  mongodb:
    container_name: mongodb
    image: mongo
    build:
      context: ./docker-config/mongodb
    expose:
      - 27017
    ports:
      - 27017:27017
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./data/mongodb:/data/db
    logging:
      options:
        max-size: 1g
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONG<PERSON>_INITDB_ROOT_PASSWORD: example
      MONGO_INITDB_DATABASE: first-spark
    command: ["--bind_ip_all", "--port", "27017"]
#    healthcheck:
#      test: echo "try { rs.status() } catch (err) { rs.initiate({_id:'rs0',members:[{_id:0,host:'host.docker.internal:27017'}]}) }" | mongosh --port 27017 --quiet
#      interval: 5s
#      timeout: 30s
#      start_period: 0s
#      start_interval: 1s
#      retries: 30
    networks:
      - app-network
    restart: unless-stopped

  mongo-express:
    container_name: mongo-express
    image: mongo-express
    ports:
      - 8081:8081
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: example
      ME_CONFIG_MONGODB_URL: ************************************/
      ME_CONFIG_BASICAUTH: false
    networks:
      - app-network
    restart: unless-stopped

  nginx:
    container_name: nginx
    image: nginx
    build:
      context: ./docker-config/nginx
    read_only: true
    tmpfs:
      - /var/cache/nginx
      - /run
    ports:
      - "80:80"
      - "443:443"
    networks:
      - app-network
    depends_on:
      - fsdata
    restart: unless-stopped

  redis:
    image: redis:alpine
    container_name: redis
    # from https://stackoverflow.com/a/70706628
    healthcheck:
      test: [ "CMD", "redis-cli", "--raw", "incr", "ping" ]
    expose:
      - 6379
    networks:
      - app-network
    restart: unless-stopped

  nats:
    image: nats:latest
    container_name: nats
    command: ["--jetstream", "--store_dir", "/data"]
    ports:
      - "4222:4222"
      - "8222:8222"  # HTTP monitoring port
    volumes:
      - ./data/nats:/data
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge
